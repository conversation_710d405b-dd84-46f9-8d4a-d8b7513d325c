#!/bin/bash

# Instalační skript pro WiFi penetrační nástroje
# POUZE PRO VZDĚLÁVACÍ ÚČELY A VLASTNÍ SÍTĚ!

echo "🔧 Instalace WiFi penetračních nástrojů"
echo "========================================"
echo "⚠️  POUZE PRO VZDĚLÁVACÍ ÚČELY A VLASTNÍ SÍTĚ!"
echo "========================================"

# Kontrola root práv
if [[ $EUID -eq 0 ]]; then
   echo "❌ Nespouštějte tento skript jako root!"
   echo "💡 Spusťte jako běžný uživatel, sudo se použije automaticky"
   exit 1
fi

# Aktualizace systému
echo "📦 Aktualizuji systém..."
sudo apt update

# Základní nástroje
echo "🔧 Instaluji základn<PERSON> nástroje..."
sudo apt install -y \
    aircrack-ng \
    reaver \
    hashcat \
    john \
    crunch \
    cowpatty \
    pyrit \
    macchanger \
    hcxtools \
    hcxdumptool \
    wireless-tools \
    net-tools \
    curl \
    wget \
    git \
    python3-pip

# Python knihovny
echo "🐍 Instaluji Python knihovny..."
pip3 install --user \
    scapy \
    netaddr \
    requests \
    colorama \
    tqdm

# Kontrola instalace
echo "✅ Kontrola instalace..."

tools=("aircrack-ng" "reaver" "hashcat" "john" "crunch" "hcxtools")
missing_tools=()

for tool in "${tools[@]}"; do
    if ! command -v "$tool" &> /dev/null; then
        missing_tools+=("$tool")
        echo "❌ $tool - CHYBÍ"
    else
        echo "✅ $tool - OK"
    fi
done

if [ ${#missing_tools[@]} -eq 0 ]; then
    echo "🎉 Všechny nástroje byly úspěšně nainstalovány!"
else
    echo "⚠️  Některé nástroje se nepodařilo nainstalovat:"
    printf '%s\n' "${missing_tools[@]}"
fi

# Vytvoření pracovního adresáře
echo "📁 Vytvářím pracovní adresář..."
mkdir -p ~/wifi_tools
cd ~/wifi_tools

# Stažení dodatečných nástrojů
echo "📥 Stahuji dodatečné nástroje..."

# Airgeddon
if [ ! -d "airgeddon" ]; then
    echo "📥 Stahuji airgeddon..."
    git clone https://github.com/v1s1t0r1sh3r3/airgeddon.git
fi

# Wifite2
if [ ! -d "wifite2" ]; then
    echo "📥 Stahuji wifite2..."
    git clone https://github.com/derv82/wifite2.git
fi

# Fluxion
if [ ! -d "fluxion" ]; then
    echo "📥 Stahuji fluxion..."
    git clone https://github.com/FluxionNetwork/fluxion.git
fi

echo "🎯 Instalace dokončena!"
echo ""
echo "📋 Dostupné nástroje:"
echo "   • aircrack-ng - Základní WiFi cracking"
echo "   • reaver - WPS útoky"
echo "   • hashcat - Pokročilé cracking hesel"
echo "   • john - John the Ripper"
echo "   • wifite2 - Automatizovaný WiFi útok"
echo "   • airgeddon - Komplexní WiFi audit"
echo "   • fluxion - Evil Twin útoky"
echo ""
echo "💡 Použití:"
echo "   sudo aircrack-ng -w wordlist.txt capture.cap"
echo "   sudo reaver -i wlan0 -b [BSSID] -vv"
echo "   hashcat -m 2500 capture.hccapx wordlist.txt"
echo ""
echo "⚠️  DŮLEŽITÉ UPOZORNĚNÍ:"
echo "   Tyto nástroje používejte pouze na vlastních sítích"
echo "   nebo s výslovným povolením majitele sítě!"
