#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON><PERSON> slovn<PERSON>u hesel pro WiFi testování
"""

import itertools
import string

def generate_common_passwords():
    """Generuje seznam běžných hesel"""
    passwords = [
        # <PERSON><PERSON><PERSON><PERSON><PERSON> hesla
        'password', 'admin', 'guest', 'test', 'user',
        '12345678', '123456789', '1234567890',
        'qwerty', 'abc123', 'password123',
        
        # WiFi specifická
        'freewifi', 'wifi', 'internet', 'access', 'public',
        'welcome', 'connect', 'network', 'wireless',
        
        # Roky
        '2023', '2024', '2025',
        
        # <PERSON><PERSON><PERSON><PERSON>
        'admin123', 'guest123', 'wifi123', 'test123',
        'password1', 'password2', 'admin1', 'admin2',
        
        # Pr<PERSON><PERSON><PERSON><PERSON> he<PERSON>
        '',
        
        # <PERSON><PERSON>la
        '00000000', '11111111', '22222222', '88888888',
        '12121212', '87654321',
        
        # <PERSON><PERSON>á<PERSON>nic<PERSON> vzory
        'qwertyui', 'asdfghjk', 'zxcvbnm',
        '1q2w3e4r', 'qwer1234',
        
        # Společnosti/značky
        'linksys', 'netgear', 'dlink', 'tplink',
        'cisco', 'belkin', 'asus', 'huawei'
    ]
    
    return passwords

def generate_numeric_passwords(length=8):
    """Generuje číselná hesla dané délky"""
    passwords = []
    
    # Opakující se číslice
    for digit in '0123456789':
        passwords.append(digit * length)
    
    # Vzestupné/sestupné sekvence
    if length <= 10:
        passwords.append(''.join(str(i) for i in range(length)))
        passwords.append(''.join(str(i) for i in range(length-1, -1, -1)))
    
    return passwords

def generate_date_passwords():
    """Generuje hesla založená na datech"""
    passwords = []
    
    # Roky
    for year in range(2020, 2026):
        passwords.append(str(year))
        passwords.append(f"wifi{year}")
        passwords.append(f"password{year}")
    
    # Měsíce/dny
    for month in range(1, 13):
        for day in range(1, 32):
            if month <= 12 and day <= 31:
                passwords.append(f"{month:02d}{day:02d}{2024}")
                passwords.append(f"{day:02d}{month:02d}{2024}")
    
    return passwords[:100]  # Omezit počet

def generate_wordlist_file(filename="wordlist.txt"):
    """Vytvoří soubor se slovníkem hesel"""
    all_passwords = set()  # Použít set pro odstranění duplikátů
    
    # Přidat různé typy hesel
    all_passwords.update(generate_common_passwords())
    all_passwords.update(generate_numeric_passwords(8))
    all_passwords.update(generate_numeric_passwords(10))
    all_passwords.update(generate_date_passwords())
    
    # Převést na seznam a seřadit
    password_list = sorted(list(all_passwords))
    
    # Zapsat do souboru
    with open(filename, 'w', encoding='utf-8') as f:
        for password in password_list:
            f.write(f"{password}\n")
    
    print(f"📝 Vytvořen slovník s {len(password_list)} hesly: {filename}")
    return password_list

def load_wordlist(filename):
    """Načte slovník hesel ze souboru"""
    try:
        with open(filename, 'r', encoding='utf-8') as f:
            passwords = [line.strip() for line in f if line.strip()]
        print(f"📖 Načten slovník s {len(passwords)} hesly z {filename}")
        return passwords
    except FileNotFoundError:
        print(f"❌ Soubor {filename} nenalezen")
        return []

if __name__ == "__main__":
    print("🔑 Generátor slovníku hesel pro WiFi")
    print("=" * 40)
    
    # Generovat slovník
    wordlist = generate_wordlist_file()
    
    print(f"\n📊 Statistiky:")
    print(f"   Celkem hesel: {len(wordlist)}")
    print(f"   Prázdná hesla: {wordlist.count('')}")
    print(f"   Číselná hesla: {len([p for p in wordlist if p.isdigit()])}")
    
    print(f"\n💡 Prvních 20 hesel:")
    for i, pwd in enumerate(wordlist[:20], 1):
        display_pwd = repr(pwd) if pwd == '' else pwd
        print(f"   {i:2d}. {display_pwd}")
    
    if len(wordlist) > 20:
        print(f"   ... a dalších {len(wordlist) - 20} hesel")
